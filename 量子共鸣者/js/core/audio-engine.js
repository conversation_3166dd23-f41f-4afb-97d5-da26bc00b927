/**
 * 量子共鸣者 - 音频引擎
 * 基于Web Audio API实现音频处理、频率分析、动态音乐生成
 */

class AudioEngine {
    constructor() {
        this.audioContext = null;
        this.masterGain = null;
        this.musicGain = null;
        this.sfxGain = null;
        this.analyser = null;
        this.oscillators = new Map(); // 存储振荡器
        this.audioBuffers = new Map(); // 存储音频缓冲区
        
        // 音频分析数据
        this.frequencyData = null;
        this.timeData = null;
        this.sampleRate = 44100;
        this.fftSize = 2048;
        
        // 音量设置
        this.volumes = {
            master: 0.8,
            music: 0.7,
            sfx: 0.9
        };
        
        // 当前播放的音频
        this.currentMusic = null;
        this.isInitialized = false;
        
        console.log('🎵 音频引擎已创建');
    }

    /**
     * 初始化音频引擎
     * 注意：需要用户交互后才能创建AudioContext
     */
    async init() {
        try {
            console.log('🎵 开始初始化音频引擎...');

            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.sampleRate = this.audioContext.sampleRate;
            console.log('🎵 音频上下文已创建，采样率:', this.sampleRate);

            // 创建主增益节点
            this.masterGain = this.audioContext.createGain();
            this.masterGain.gain.value = this.volumes.master;
            this.masterGain.connect(this.audioContext.destination);

            // 创建音乐增益节点
            this.musicGain = this.audioContext.createGain();
            this.musicGain.gain.value = this.volumes.music;
            this.musicGain.connect(this.masterGain);

            // 创建音效增益节点
            this.sfxGain = this.audioContext.createGain();
            this.sfxGain.gain.value = this.volumes.sfx;
            this.sfxGain.connect(this.masterGain);

            // 创建分析器节点
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = this.fftSize;
            this.analyser.connect(this.masterGain);

            // 初始化分析数据数组
            this.frequencyData = new Uint8Array(this.analyser.frequencyBinCount);
            this.timeData = new Uint8Array(this.analyser.frequencyBinCount);

            console.log('🎵 音频节点已创建');

            // 恢复音频上下文（如果被暂停）
            if (this.audioContext.state === 'suspended') {
                console.log('🎵 恢复音频上下文...');
                await this.audioContext.resume();
            }

            this.isInitialized = true;
            console.log('🎵 音频引擎核心初始化成功');

            // 加载设置（使用超时机制）
            try {
                await Promise.race([
                    this.loadSettings(),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('设置加载超时')), 5000)
                    )
                ]);
            } catch (error) {
                console.warn('⚠️ 音频设置加载失败，使用默认设置:', error.message);
            }

            console.log('✅ 音频引擎初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 音频引擎初始化失败:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * 检查音频引擎是否已初始化
     */
    isReady() {
        return this.isInitialized && this.audioContext && this.audioContext.state === 'running';
    }

    /**
     * 创建振荡器（用于生成特定频率的音调）
     * @param {number} frequency - 频率 (Hz)
     * @param {string} type - 波形类型 ('sine', 'square', 'sawtooth', 'triangle')
     * @param {number} duration - 持续时间 (秒)
     * @param {number} volume - 音量 (0-1)
     * @returns {OscillatorNode} 振荡器节点
     */
    createOscillator(frequency, type = 'sine', duration = 1, volume = 0.3) {
        if (!this.isReady()) return null;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            // 设置振荡器参数
            oscillator.type = type;
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            
            // 设置音量包络（ADSR）
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01); // Attack
            gainNode.gain.exponentialRampToValueAtTime(volume * 0.8, this.audioContext.currentTime + 0.1); // Decay
            gainNode.gain.setValueAtTime(volume * 0.6, this.audioContext.currentTime + duration - 0.1); // Sustain
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration); // Release
            
            // 连接节点
            oscillator.connect(gainNode);
            gainNode.connect(this.sfxGain);
            
            return { oscillator, gainNode };
        } catch (error) {
            console.error('❌ 振荡器创建失败:', error);
            return null;
        }
    }

    /**
     * 播放指定频率的音调
     * @param {number} frequency - 频率 (Hz)
     * @param {number} duration - 持续时间 (秒)
     * @param {string} type - 波形类型
     * @param {number} volume - 音量
     */
    playTone(frequency, duration = 0.2, type = 'sine', volume = 0.3) {
        const nodes = this.createOscillator(frequency, type, duration, volume);
        if (!nodes) return;
        
        const { oscillator } = nodes;
        
        // 开始播放
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
        
        // 清理
        oscillator.onended = () => {
            oscillator.disconnect();
        };
    }

    /**
     * 播放共鸣音效
     * @param {number} frequency - 基础频率
     * @param {number} resonanceStrength - 共鸣强度 (0-1)
     */
    playResonanceSound(frequency, resonanceStrength) {
        if (!this.isReady()) return;
        
        // 基础音调
        this.playTone(frequency, 0.3, 'sine', 0.2 * resonanceStrength);
        
        // 和声（五度音程）
        this.playTone(frequency * 1.5, 0.25, 'sine', 0.15 * resonanceStrength);
        
        // 泛音
        this.playTone(frequency * 2, 0.2, 'triangle', 0.1 * resonanceStrength);
        
        // 低频共鸣
        this.playTone(frequency * 0.5, 0.4, 'sawtooth', 0.05 * resonanceStrength);
    }

    /**
     * 播放粒子激活音效
     * @param {number} frequency - 粒子频率
     * @param {number} energy - 能量级别
     */
    playParticleActivation(frequency, energy) {
        if (!this.isReady()) return;
        
        // 创建复杂的音效
        const baseVolume = Math.min(0.4, energy * 0.1);
        
        // 主音调 - 上升音调
        const nodes = this.createOscillator(frequency, 'sine', 0.15, baseVolume);
        if (nodes) {
            const { oscillator } = nodes;
            oscillator.frequency.exponentialRampToValueAtTime(
                frequency * 1.5, 
                this.audioContext.currentTime + 0.1
            );
            oscillator.start();
            oscillator.stop(this.audioContext.currentTime + 0.15);
        }
        
        // 噪声爆发
        setTimeout(() => {
            this.playTone(frequency * 2, 0.05, 'square', baseVolume * 0.3);
        }, 50);
    }

    /**
     * 播放连锁反应音效
     * @param {number} chainLength - 连锁长度
     * @param {Array} frequencies - 频率数组
     */
    playChainReaction(chainLength, frequencies) {
        if (!this.isReady() || !frequencies.length) return;
        
        frequencies.forEach((freq, index) => {
            setTimeout(() => {
                const volume = Math.max(0.1, 0.4 - index * 0.05);
                const pitch = freq * (1 + index * 0.1); // 音调逐渐升高
                this.playTone(pitch, 0.2, 'triangle', volume);
            }, index * 100); // 每100ms播放一个音符
        });
    }

    /**
     * 生成背景环境音
     * @param {number} baseFrequency - 基础频率
     * @param {number} complexity - 复杂度 (1-10)
     */
    generateAmbientSound(baseFrequency = 220, complexity = 5) {
        if (!this.isReady()) return;
        
        // 停止之前的环境音
        this.stopAmbientSound();
        
        const ambientOscillators = [];
        
        // 创建多个振荡器形成和弦
        for (let i = 0; i < complexity; i++) {
            const freq = baseFrequency * Math.pow(2, i / 12); // 半音阶
            const nodes = this.createOscillator(freq, 'sine', 10, 0.05);
            
            if (nodes) {
                const { oscillator, gainNode } = nodes;
                
                // 添加轻微的频率调制
                const lfo = this.audioContext.createOscillator();
                const lfoGain = this.audioContext.createGain();
                lfo.frequency.value = 0.1 + Math.random() * 0.2;
                lfoGain.gain.value = 2;
                lfo.connect(lfoGain);
                lfoGain.connect(oscillator.frequency);
                
                oscillator.start();
                lfo.start();
                
                ambientOscillators.push({ oscillator, lfo });
            }
        }
        
        this.ambientOscillators = ambientOscillators;
    }

    /**
     * 停止环境音
     */
    stopAmbientSound() {
        if (this.ambientOscillators) {
            this.ambientOscillators.forEach(({ oscillator, lfo }) => {
                try {
                    oscillator.stop();
                    lfo.stop();
                } catch (e) {
                    // 忽略已经停止的振荡器错误
                }
            });
            this.ambientOscillators = null;
        }
    }

    /**
     * 更新音频分析数据
     */
    updateAnalysis() {
        if (!this.analyser) return;
        
        this.analyser.getByteFrequencyData(this.frequencyData);
        this.analyser.getByteTimeDomainData(this.timeData);
    }

    /**
     * 获取频率数据
     * @returns {Uint8Array} 频率数据数组
     */
    getFrequencyData() {
        this.updateAnalysis();
        return this.frequencyData;
    }

    /**
     * 获取时域数据
     * @returns {Uint8Array} 时域数据数组
     */
    getTimeData() {
        this.updateAnalysis();
        return this.timeData;
    }

    /**
     * 获取指定频率范围的平均音量
     * @param {number} minFreq - 最小频率
     * @param {number} maxFreq - 最大频率
     * @returns {number} 平均音量 (0-255)
     */
    getFrequencyRangeVolume(minFreq, maxFreq) {
        if (!this.frequencyData) return 0;
        
        const nyquist = this.sampleRate / 2;
        const minBin = Math.floor((minFreq / nyquist) * this.frequencyData.length);
        const maxBin = Math.floor((maxFreq / nyquist) * this.frequencyData.length);
        
        let sum = 0;
        let count = 0;
        
        for (let i = minBin; i <= maxBin && i < this.frequencyData.length; i++) {
            sum += this.frequencyData[i];
            count++;
        }
        
        return count > 0 ? sum / count : 0;
    }

    /**
     * 设置音量
     * @param {string} type - 音量类型 ('master', 'music', 'sfx')
     * @param {number} volume - 音量值 (0-1)
     */
    setVolume(type, volume) {
        volume = MathUtils.clamp(volume, 0, 1);
        this.volumes[type] = volume;
        
        switch (type) {
            case 'master':
                if (this.masterGain) {
                    this.masterGain.gain.setValueAtTime(volume, this.audioContext.currentTime);
                }
                break;
            case 'music':
                if (this.musicGain) {
                    this.musicGain.gain.setValueAtTime(volume, this.audioContext.currentTime);
                }
                break;
            case 'sfx':
                if (this.sfxGain) {
                    this.sfxGain.gain.setValueAtTime(volume, this.audioContext.currentTime);
                }
                break;
        }
        
        // 保存设置
        this.saveSettings();
    }

    /**
     * 获取音量
     * @param {string} type - 音量类型
     * @returns {number} 音量值
     */
    getVolume(type) {
        return this.volumes[type] || 0;
    }

    /**
     * 保存音频设置
     */
    async saveSettings() {
        try {
            await storageService.put('audio.settings', {
                volumes: this.volumes,
                fftSize: this.fftSize
            });
        } catch (error) {
            console.error('❌ 音频设置保存失败:', error);
        }
    }

    /**
     * 加载音频设置
     */
    async loadSettings() {
        try {
            // 确保存储服务已初始化
            if (!window.storageService) {
                console.warn('⚠️ 存储服务未初始化，跳过音频设置加载');
                return;
            }

            // 等待存储服务初始化完成
            await window.storageService.waitForInit();

            const settings = await storageService.get('audio.settings');
            if (settings) {
                if (settings.volumes) {
                    Object.assign(this.volumes, settings.volumes);
                    // 应用音量设置
                    Object.keys(this.volumes).forEach(type => {
                        this.setVolume(type, this.volumes[type]);
                    });
                }
                if (settings.fftSize) {
                    this.fftSize = settings.fftSize;
                }
            }
            console.log('🎵 音频设置加载完成');
        } catch (error) {
            console.error('❌ 音频设置加载失败:', error);
            // 即使设置加载失败，也不应该阻止音频引擎初始化
        }
    }

    /**
     * 暂停音频上下文
     */
    suspend() {
        if (this.audioContext && this.audioContext.state === 'running') {
            this.audioContext.suspend();
        }
    }

    /**
     * 恢复音频上下文
     */
    resume() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    /**
     * 创建音频序列器
     * @param {Array} sequence - 音频序列配置
     * @returns {AudioSequencer} 序列器对象
     */
    createSequencer(sequence) {
        return new AudioSequencer(this, sequence);
    }

    /**
     * 创建音频合成器
     * @param {Object} config - 合成器配置
     * @returns {AudioSynthesizer} 合成器对象
     */
    createSynthesizer(config = {}) {
        return new AudioSynthesizer(this, config);
    }

    /**
     * 创建音频效果器
     * @param {string} type - 效果器类型
     * @param {Object} params - 效果器参数
     * @returns {AudioEffect} 效果器对象
     */
    createEffect(type, params = {}) {
        return new AudioEffect(this, type, params);
    }

    /**
     * 获取音频上下文时间
     * @returns {number} 当前音频时间
     */
    getCurrentTime() {
        return this.audioContext ? this.audioContext.currentTime : 0;
    }

    /**
     * 调度音频事件
     * @param {Function} callback - 回调函数
     * @param {number} time - 调度时间
     */
    scheduleCallback(callback, time) {
        if (!this.audioContext) return;

        const bufferSource = this.audioContext.createBufferSource();
        bufferSource.buffer = this.audioContext.createBuffer(1, 1, this.audioContext.sampleRate);
        bufferSource.onended = callback;
        bufferSource.connect(this.audioContext.destination);
        bufferSource.start(time);
    }

    /**
     * 销毁音频引擎
     */
    destroy() {
        this.stopAmbientSound();

        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }

        this.oscillators.clear();
        this.audioBuffers.clear();
        this.isInitialized = false;

        console.log('🎵 音频引擎已销毁');
    }
}

/**
 * 音频序列器 - 用于创建音乐序列和节拍
 */
class AudioSequencer {
    constructor(audioEngine, sequence = []) {
        this.audioEngine = audioEngine;
        this.sequence = sequence;
        this.isPlaying = false;
        this.currentStep = 0;
        this.bpm = 120;
        this.stepDuration = 60 / this.bpm / 4; // 16分音符
        this.nextStepTime = 0;
        this.lookahead = 25.0; // 毫秒
        this.scheduleAheadTime = 0.1; // 秒
        this.timerID = null;

        // 事件回调
        this.onStep = null;
        this.onBeat = null;

        console.log('🎼 音频序列器已创建');
    }

    /**
     * 设置BPM
     * @param {number} bpm - 每分钟节拍数
     */
    setBPM(bpm) {
        this.bpm = bpm;
        this.stepDuration = 60 / this.bpm / 4;
    }

    /**
     * 设置序列
     * @param {Array} sequence - 音频序列
     */
    setSequence(sequence) {
        this.sequence = sequence;
    }

    /**
     * 开始播放
     */
    start() {
        if (this.isPlaying) return;

        this.isPlaying = true;
        this.currentStep = 0;
        this.nextStepTime = this.audioEngine.getCurrentTime();
        this.scheduler();

        console.log('▶️ 音频序列器开始播放');
    }

    /**
     * 停止播放
     */
    stop() {
        this.isPlaying = false;
        if (this.timerID) {
            clearTimeout(this.timerID);
            this.timerID = null;
        }

        console.log('⏹️ 音频序列器停止播放');
    }

    /**
     * 调度器
     */
    scheduler() {
        while (this.nextStepTime < this.audioEngine.getCurrentTime() + this.scheduleAheadTime) {
            this.scheduleStep(this.currentStep, this.nextStepTime);
            this.nextStep();
        }

        if (this.isPlaying) {
            this.timerID = setTimeout(() => this.scheduler(), this.lookahead);
        }
    }

    /**
     * 调度单个步骤
     * @param {number} step - 步骤索引
     * @param {number} time - 播放时间
     */
    scheduleStep(step, time) {
        if (this.sequence.length === 0) return;

        const stepData = this.sequence[step % this.sequence.length];

        if (stepData && stepData.active) {
            // 播放音符
            if (stepData.frequency) {
                this.audioEngine.playTone(
                    stepData.frequency,
                    stepData.duration || this.stepDuration,
                    stepData.volume || 0.5,
                    time
                );
            }

            // 播放打击乐
            if (stepData.drum) {
                this.audioEngine.playDrumSound(stepData.drum, stepData.volume || 0.5, time);
            }
        }

        // 触发步骤回调
        if (this.onStep) {
            this.onStep(step, stepData);
        }

        // 触发节拍回调（每4步一拍）
        if (step % 4 === 0 && this.onBeat) {
            this.onBeat(Math.floor(step / 4));
        }
    }

    /**
     * 下一步
     */
    nextStep() {
        this.nextStepTime += this.stepDuration;
        this.currentStep++;

        // 循环播放
        if (this.currentStep >= this.sequence.length) {
            this.currentStep = 0;
        }
    }

    /**
     * 获取当前步骤
     * @returns {number} 当前步骤索引
     */
    getCurrentStep() {
        return this.currentStep;
    }

    /**
     * 获取当前节拍
     * @returns {number} 当前节拍
     */
    getCurrentBeat() {
        return Math.floor(this.currentStep / 4);
    }

    /**
     * 销毁序列器
     */
    destroy() {
        this.stop();
        this.sequence = [];
        this.onStep = null;
        this.onBeat = null;
    }
}

// 导出类到全局作用域
window.AudioEngine = AudioEngine;

// 创建全局音频引擎实例
window.audioEngine = new AudioEngine();
